import argparse
import joblib
import numpy as np
from classifier.feature_extractor import extract_features
from config import config_loader

def classify_image(image_path, model=None, return_confidence=False):
    """
    Classify a weapon/aircraft image for REDFOR detection

    Args:
        image_path: Path to the image file
        model: Pre-loaded classifier model (optional)
        return_confidence: Whether to return confidence scores

    Returns:
        If return_confidence=False: predicted class name
        If return_confidence=True: (predicted_class, confidence_score, all_probabilities)
    """
    if model is None:
        config = config_loader.load_config()
        classifier_path = config['paths']['classifier_pkl']
        try:
            model = joblib.load(classifier_path)
        except FileNotFoundError:
            raise FileNotFoundError(f"Classifier model not found at {classifier_path}. Please train a model first.")

    features = extract_features(image_path)
    prediction = model.predict([features])[0]

    if return_confidence:
        # Get prediction probabilities if available
        if hasattr(model, 'predict_proba'):
            probabilities = model.predict_proba([features])[0]
            confidence = np.max(probabilities)

            # Get class names if available
            if hasattr(model, 'classes_'):
                class_probs = dict(zip(model.classes_, probabilities))
            else:
                class_probs = {}

            return prediction, confidence, class_probs
        else:
            return prediction, 1.0, {}

    return prediction

def get_weapon_info(weapon_class):
    """Get additional information about detected REDFOR weapon"""
    config = config_loader.load_config()
    weapon_classes = config.get('weapon_classes', {})

    for country, weapons in weapon_classes.items():
        if weapon_class in weapons:
            return {
                'weapon': weapon_class,
                'country': country.upper(),
                'threat_level': 'HIGH' if country in ['russian', 'chinese'] else 'MEDIUM'
            }

    return {
        'weapon': weapon_class,
        'country': 'UNKNOWN',
        'threat_level': 'UNKNOWN'
    }

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("image", help="Path to input image")
    args = parser.parse_args()

    prediction = classify_image(args.image)
    print(f"Predicted class: {prediction}")
