import argparse
import joblib
import numpy as np
from classifier.feature_extractor import extract_features
from config import config_loader

def classify_image(image_path, model=None, return_confidence=False):
    """
    Classify a weapon/aircraft image for REDFOR detection

    Args:
        image_path: Path to the image file
        model: Pre-loaded classifier model (optional)
        return_confidence: Whether to return confidence scores

    Returns:
        If return_confidence=False: predicted class name
        If return_confidence=True: (predicted_class, confidence_score, all_probabilities)
    """
    if model is None:
        config = config_loader.load_config()
        classifier_path = config['paths']['classifier_pkl']
        try:
            model = joblib.load(classifier_path)
        except FileNotFoundError:
            raise FileNotFoundError(f"Classifier model not found at {classifier_path}. Please train a model first.")

    features = extract_features(image_path)
    prediction = model.predict([features])[0]

    if return_confidence:
        # Get prediction probabilities if available
        if hasattr(model, 'predict_proba'):
            probabilities = model.predict_proba([features])[0]
            confidence = np.max(probabilities)

            # Get class names if available
            if hasattr(model, 'classes_'):
                class_probs = dict(zip(model.classes_, probabilities))
            else:
                class_probs = {}

            return prediction, confidence, class_probs
        else:
            return prediction, 1.0, {}

    return prediction

def get_weapon_info(weapon_class):
    """Get detailed information about detected Iranian/REDFOR weapon"""
    config = config_loader.load_config()
    weapon_classes = config.get('weapon_classes', {})

    # Iranian missile database with ranges and capabilities
    iranian_missile_specs = {
        # Ballistic Missiles
        'Fateh-110': {'range': '300km', 'type': 'Ballistic Missile', 'threat': 'HIGH', 'precision': 'High'},
        'Fateh-313': {'range': '500km', 'type': 'Ballistic Missile', 'threat': 'HIGH', 'precision': 'High'},
        'Zolfaghar': {'range': '700km', 'type': 'Ballistic Missile', 'threat': 'CRITICAL', 'precision': 'High'},
        'Dezful': {'range': '1000km', 'type': 'Ballistic Missile', 'threat': 'CRITICAL', 'precision': 'High'},
        'Qiam-1': {'range': '800km', 'type': 'Ballistic Missile', 'threat': 'HIGH', 'precision': 'Medium'},
        'Shahab-3': {'range': '1300km', 'type': 'Ballistic Missile', 'threat': 'CRITICAL', 'precision': 'Medium'},
        'Emad': {'range': '1700km', 'type': 'Ballistic Missile', 'threat': 'CRITICAL', 'precision': 'High'},
        'Khorramshahr': {'range': '2000km', 'type': 'MIRV Ballistic Missile', 'threat': 'EXTREME', 'precision': 'High'},
        'Kheibar-Shekan': {'range': '1450km', 'type': 'Hypersonic Missile', 'threat': 'EXTREME', 'precision': 'High'},
        'Fattah': {'range': '1400km', 'type': 'Hypersonic Glide Vehicle', 'threat': 'EXTREME', 'precision': 'High'},
        'Fattah-2': {'range': '1500km', 'type': 'Advanced Hypersonic', 'threat': 'EXTREME', 'precision': 'High'},

        # Artillery Rockets
        'Fajr-5': {'range': '75km', 'type': 'Artillery Rocket', 'threat': 'HIGH', 'precision': 'Low'},
        'Fajr-3': {'range': '43km', 'type': 'Artillery Rocket', 'threat': 'MEDIUM', 'precision': 'Low'},
        'Zelzal-1': {'range': '125km', 'type': 'Heavy Rocket', 'threat': 'HIGH', 'precision': 'Low'},
        'Zelzal-2': {'range': '210km', 'type': 'Heavy Rocket', 'threat': 'HIGH', 'precision': 'Medium'},

        # Drones/Loitering Munitions
        'Shahed-136': {'range': '2500km', 'type': 'Kamikaze Drone', 'threat': 'HIGH', 'precision': 'High'},
        'Shahed-131': {'range': '900km', 'type': 'Kamikaze Drone', 'threat': 'MEDIUM', 'precision': 'Medium'},
        'Geran-2': {'range': '2500km', 'type': 'Kamikaze Drone (Russian designation)', 'threat': 'HIGH', 'precision': 'High'},

        # Cruise Missiles
        'Hoveyzeh': {'range': '1350km', 'type': 'Cruise Missile', 'threat': 'CRITICAL', 'precision': 'High'},
        'Paveh': {'range': '1650km', 'type': 'Cruise Missile', 'threat': 'CRITICAL', 'precision': 'High'},
        'Soumar': {'range': '2500km', 'type': 'Cruise Missile', 'threat': 'CRITICAL', 'precision': 'High'},
    }

    # Find weapon category and country
    weapon_country = 'UNKNOWN'
    weapon_category = 'UNKNOWN'

    for category, weapons in weapon_classes.items():
        if weapon_class in weapons:
            weapon_category = category
            if 'iranian' in category.lower():
                weapon_country = 'IRANIAN'
            elif 'russian' in category.lower():
                weapon_country = 'RUSSIAN'
            elif 'chinese' in category.lower():
                weapon_country = 'CHINESE'
            elif 'hezbollah' in category.lower():
                weapon_country = 'HEZBOLLAH/IRANIAN'
            break

    # Get detailed specs if available
    specs = iranian_missile_specs.get(weapon_class, {
        'range': 'Unknown',
        'type': 'Unknown Weapon',
        'threat': 'MEDIUM',
        'precision': 'Unknown'
    })

    return {
        'weapon': weapon_class,
        'country': weapon_country,
        'category': weapon_category,
        'threat_level': specs['threat'],
        'weapon_type': specs['type'],
        'range': specs['range'],
        'precision': specs['precision'],
        'tel_aviv_threat': 'YES' if any(x in specs['range'] for x in ['300km', '500km', '700km', '1000km', '1300km', '1400km', '1450km', '1500km', '1650km', '1700km', '2000km', '2500km']) else 'UNKNOWN'
    }

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("image", help="Path to input image")
    args = parser.parse_args()

    prediction = classify_image(args.image)
    print(f"Predicted class: {prediction}")
