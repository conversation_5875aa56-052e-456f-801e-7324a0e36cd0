import argparse
import joblib
from classifier.feature_extractor import extract_features
from config import config_loader

def classify_image(image_path, model=None):
    if model is None:
        config = config_loader.load_config()
        classifier_path = config['paths']['classifier_pkl']
        model = joblib.load(classifier_path)
    
    features = extract_features(image_path)
    prediction = model.predict([features])
    return prediction[0]

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("image", help="Path to input image")
    args = parser.parse_args()

    prediction = classify_image(args.image)
    print(f"Predicted class: {prediction}")
