# 9M83ME - Anti-REDFOR Detection System

An AI-powered weapon identification system designed to detect and classify Russian, Iranian, and Chinese military assets for IFF (Identification Friend or Foe) purposes.

## 🎯 Purpose

This system identifies specific REDFOR (Red Forces) weapons and aircraft including:

### Russian Weapons
- **Geran-2** (Loitering munition)
- **Kh-101** (Cruise missile)
- **Kalibr** (Cruise missile)
- **Iskander** (Ballistic missile)
- **Su-57** (Fighter aircraft)
- **Su-35** (Fighter aircraft)
- **Tu-95** (Strategic bomber)
- **Kinzhal** (Hypersonic missile)

### Iranian Weapons
- **Fajr-5** (Artillery rocket)
- **Fateh-110** (Ballistic missile)
- **Zolfaghar** (Ballistic missile)
- **Shahed-136** (Loitering munition)
- **Mohajer-6** (Combat drone)

### Chinese Weapons
- **DF-21** (Ballistic missile)
- **DF-26** (Ballistic missile)
- **J-20** (Fighter aircraft)
- **WZ-8** (Reconnaissance drone)
- **CH-4** (Combat drone)

## 🚀 Quick Start

### 1. Select Your ONNX Model
```bash
python main.py --select-model
```

### 2. Classify a Single Image
```bash
# Interactive file selection
python main.py --infer-interactive

# Direct path
python main.py --infer path/to/weapon_image.jpg
```

### 3. Batch Classification
```bash
# Classify all images in data/input_images/
python main.py --batch-classify
```

### 4. Train Your Own Model
```bash
# Put images in folders like:
# data/train_images/Geran-2/image1.jpg
# data/train_images/Fajr-5/image2.jpg
python main.py --train
```

### 5. GUI Interface
```bash
python main.py --gui
```

## 📁 Project Structure

```
9M83ME/
├── main.py                 # Main application
├── config/
│   ├── config.yaml         # REDFOR weapon classes & settings
│   └── config_loader.py    # Configuration loader
├── classifier/
│   ├── classifier.py       # Main classification logic
│   ├── feature_extractor.py # ONNX feature extraction
│   ├── train_classifier.py # Training pipeline
│   └── classify_folder.py  # Batch processing
├── models/
│   ├── *.onnx             # Your ONNX models
│   └── redfor_classifier.pkl # Trained classifier
├── data/
│   ├── input_images/      # Images to classify
│   └── train_images/      # Training data by weapon class
└── ui/                    # GUI components
```

## 🔧 Requirements

- Python 3.8+
- ONNX Runtime
- scikit-learn
- PyTorch/torchvision
- PIL (Pillow)
- PyQt5 (for GUI)
- PyYAML

## 📊 Output Format

When classifying an image, you'll get:

```
🎯 REDFOR DETECTION RESULTS
==================================================
📷 Image: iranian_missile.jpg
🎯 Detected Weapon: Fajr-5
🌍 Country of Origin: IRANIAN
⚠️  Threat Level: MEDIUM
📊 Confidence: 87.3%

📈 Top Predictions:
   Fajr-5: 87.3%
   Fateh-110: 8.2%
   Shahed-136: 4.5%
==================================================
```

## 🎮 Usage Examples

### Example 1: Iranian Ballistic Missile
If you input an image of an Iranian Fajr missile, the system will:
- Identify it as "Fajr-5"
- Show country as "IRANIAN"
- Display threat level as "MEDIUM"
- Provide confidence score

### Example 2: Russian Aircraft
For a Su-57 image:
- Identifies as "Su-57"
- Country: "RUSSIAN"
- Threat level: "HIGH"

## 🔄 Training Your Own Model

1. Organize training images:
```
data/train_images/
├── Geran-2/
│   ├── geran1.jpg
│   └── geran2.jpg
├── Fajr-5/
│   ├── fajr1.jpg
│   └── fajr2.jpg
└── Su-57/
    ├── su57_1.jpg
    └── su57_2.jpg
```

2. Run training:
```bash
python main.py --train
```

## 🎯 Key Features

- **Multi-model Support**: Choose from multiple ONNX models
- **Interactive Image Selection**: GUI file picker
- **Confidence Scoring**: Know how certain the detection is
- **Batch Processing**: Classify multiple images at once
- **Real-time GUI**: Visual interface for analysis
- **Extensible**: Easy to add new weapon classes

This system is designed for defense and security applications requiring accurate identification of potential threats.
