# 🚀 9M83ME - Tel Aviv Defense Missile Identification System

An AI-powered Iranian missile and drone detection system specifically designed for Tel Aviv area defense. Upload photos from your phone to instantly identify incoming threats.

## 🎯 **CRITICAL FOR TEL AVIV RESIDENTS**
This system helps identify Iranian missiles, drones, and rockets that pose a direct threat to the Tel Aviv metropolitan area.

## 📱 **MOBILE PHONE INTEGRATION**
- **Upload photos directly from your phone**
- **Instant missile identification**
- **Works with iPhone HEIC and Android formats**
- **<PERSON><PERSON> process entire camera folders**

## 🚨 **IRANIAN THREAT DATABASE**
Comprehensive identification of Iranian missiles targeting Israel:

### 🚀 **Iranian Ballistic Missiles** (Direct Tel Aviv Threat)
- **Fateh-110** (300km) - Precision strike capability
- **Fateh-313** (500km) - Improved Fateh variant
- **Zolfaghar** (700km) - Anti-ship capable
- **Dezful** (1000km) - Precision guided
- **Qiam-1** (800km) - Scud variant
- **<PERSON>ab-3** (1300km) - Nuclear capable
- **Emad** (1700km) - Precision guided
- **Khorramshahr** (2000km) - MIRV capable
- **Kheibar-Shekan** (1450km) - Hypersonic
- **Fattah** (1400km) - Hypersonic glide vehicle
- **Fattah-2** (1500km) - Advanced hypersonic

### 🎯 **Iranian Artillery Rockets**
- **Fajr-5** (75km) - 333mm heavy rocket
- **Fajr-3** (43km) - 240mm rocket
- **Zelzal-1** (125km) - 610mm heavy rocket
- **Zelzal-2** (210km) - 610mm improved

### 🛸 **Iranian Drones/Kamikaze**
- **Shahed-136** (2500km) - Kamikaze drone
- **Shahed-131** (900km) - Smaller kamikaze
- **Geran-2** (2500km) - Russian-operated Shahed-136
- **Arash-2** - Long-range kamikaze

### 🚁 **Iranian Cruise Missiles**
- **Hoveyzeh** (1350km) - Land-attack cruise missile
- **Paveh** (1650km) - Advanced cruise missile
- **Soumar** (2500km) - Strategic cruise missile

### ⚡ **Hezbollah Proxy Weapons**
- **Katyusha** rockets from Lebanon
- **Burkan** heavy rockets
- **Zelzal-2-Hez** (Hezbollah variant)
- **Fateh-110-Hez** (Hezbollah variant)

## 📱 **QUICK START FOR TEL AVIV RESIDENTS**

### 1. **Setup Your AI Model**
```bash
python main.py --select-model
```
Choose your missile detection AI model (ONNX, PyTorch, etc.)

### 2. **Upload Photo from Phone** 🚨 **MOST IMPORTANT**
```bash
# Interactive photo selection (RECOMMENDED)
python main.py --infer-interactive
```
- Connects to your phone via USB/WiFi
- Opens file picker for easy photo selection
- Supports iPhone HEIC and Android formats
- Instant missile identification

### 3. **Direct Photo Analysis**
```bash
# If you already have the photo on computer
python main.py --infer path/to/missile_photo.jpg
```

### 4. **Batch Process Phone Camera Folder**
```bash
# Process entire phone camera folder at once
python main.py --mobile-batch "/path/to/phone/DCIM/Camera"
```

### 5. **Visual Interface**
```bash
python main.py --gui
```
Launch visual missile identification interface

## 📁 Project Structure

```
9M83ME/
├── main.py                 # Main application
├── config/
│   ├── config.yaml         # REDFOR weapon classes & settings
│   └── config_loader.py    # Configuration loader
├── classifier/
│   ├── classifier.py       # Main classification logic
│   ├── feature_extractor.py # ONNX feature extraction
│   ├── train_classifier.py # Training pipeline
│   └── classify_folder.py  # Batch processing
├── models/
│   ├── *.onnx             # Your ONNX models
│   └── redfor_classifier.pkl # Trained classifier
├── data/
│   ├── input_images/      # Images to classify
│   └── train_images/      # Training data by weapon class
└── ui/                    # GUI components
```

## 🔧 Requirements

- Python 3.8+
- ONNX Runtime
- scikit-learn
- PyTorch/torchvision
- PIL (Pillow)
- PyQt5 (for GUI)
- PyYAML

## 📊 Output Format

When classifying an image, you'll get:

```
🎯 REDFOR DETECTION RESULTS
==================================================
📷 Image: iranian_missile.jpg
🎯 Detected Weapon: Fajr-5
🌍 Country of Origin: IRANIAN
⚠️  Threat Level: MEDIUM
📊 Confidence: 87.3%

📈 Top Predictions:
   Fajr-5: 87.3%
   Fateh-110: 8.2%
   Shahed-136: 4.5%
==================================================
```

## 🎮 Usage Examples

### Example 1: Iranian Ballistic Missile
If you input an image of an Iranian Fajr missile, the system will:
- Identify it as "Fajr-5"
- Show country as "IRANIAN"
- Display threat level as "MEDIUM"
- Provide confidence score

### Example 2: Russian Aircraft
For a Su-57 image:
- Identifies as "Su-57"
- Country: "RUSSIAN"
- Threat level: "HIGH"

## 🔄 Training Your Own Model

1. Organize training images:
```
data/train_images/
├── Geran-2/
│   ├── geran1.jpg
│   └── geran2.jpg
├── Fajr-5/
│   ├── fajr1.jpg
│   └── fajr2.jpg
└── Su-57/
    ├── su57_1.jpg
    └── su57_2.jpg
```

2. Run training:
```bash
python main.py --train
```

## 🎯 Key Features

- **Multi-model Support**: Choose from multiple ONNX models
- **Interactive Image Selection**: GUI file picker
- **Confidence Scoring**: Know how certain the detection is
- **Batch Processing**: Classify multiple images at once
- **Real-time GUI**: Visual interface for analysis
- **Extensible**: Easy to add new weapon classes

This system is designed for defense and security applications requiring accurate identification of potential threats.
