@echo off
REM 🇮🇱 Israel Defense Forces Threat Detection System
REM Visual Studio 2022 Launcher

echo 🇮🇱 === ISRAEL DEFENSE FORCES THREAT DETECTION === 🇮🇱
echo 🎯 Visual Studio 2022 Launcher
echo.

REM Check if Visual Studio 2022 is installed
set VS2022_PATH=""
if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\devenv.exe" (
    set VS2022_PATH="C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\devenv.exe"
) else if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\devenv.exe" (
    set VS2022_PATH="C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\devenv.exe"
) else if exist "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\devenv.exe" (
    set VS2022_PATH="C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\devenv.exe"
) else (
    echo ❌ Visual Studio 2022 not found!
    echo 💡 Please install Visual Studio 2022 with Python support
    echo 🔗 Download: https://visualstudio.microsoft.com/vs/
    pause
    exit /b 1
)

echo ✅ Found Visual Studio 2022
echo 🚀 Opening Israel Defense project...
echo.

REM Launch Visual Studio 2022 with the project
%VS2022_PATH% "9M83ME.pyproj"

echo 📖 Quick Start Guide:
echo   1. Press F5 to run the system
echo   2. Or right-click main.py → Debug → Start with Arguments
echo   3. Use --help, --select-model, --infer-interactive
echo.
echo 🇮🇱 Protecting Israel from Iranian missile threats! 🇮🇱
