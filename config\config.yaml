paths:
  assets_folder: assets/
  classifier_pkl: models/redfor_classifier.pkl
  input_images: data/input_images/
  onnx_model: models/3N66.onnx
  render_output: data/generated_views/
  train_images: data/train_images/

# REDFOR weapon classes for IFF (Identification Friend or Foe)
weapon_classes:
  russian:
    - "Geran-2"
    - "Kh-101"
    - "Kali<PERSON>r"
    - "Iskander"
    - "Su-57"
    - "Su-35"
    - "Tu-95"
    - "Kinzhal"
  iranian:
    - "Fajr-5"
    - "Fateh-110"
    - "Zolfaghar"
    - "Shahed-136"
    - "Mohajer-6"
  chinese:
    - "DF-21"
    - "DF-26"
    - "J-20"
    - "WZ-8"
    - "CH-4"
render:
  background_color:
  - 255
  - 255
  - 255
  image_size:
  - 240
  - 240
  lighting: true
  views_per_model: 12
train:
  model_type: LogisticRegression
  random_seed: 42
  test_split: 0.2
  confidence_threshold: 0.7
