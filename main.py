import argparse
import os
from classifier.train_classifier import main as train_main
from renderer import generate_views
from ui import ui_viewer
from config import config_loader

CONFIG_PATH = 'config/config.yaml'


def run_renderer():
    print("[INFO] Starting 3D model rendering...")
    generate_views.main()


def run_training():
    print("[INFO] Starting training process...")
    train_main()


def run_inference(image_path):
    """Run Iranian missile detection for ENTIRE ISRAEL defense"""
    print(f"🔍 Analyzing image for threats to Israel: {os.path.basename(image_path)}")
    if not os.path.exists(image_path):
        print(f"❌ Image not found: {image_path}")
        return

    try:
        from classifier.classifier import classify_image, get_weapon_info

        # Get classification with confidence
        result, confidence, class_probs = classify_image(image_path, return_confidence=True)
        weapon_info = get_weapon_info(result)
        israel_threat = weapon_info['israel_threat']

        print("\n" + "🇮🇱" + "="*70 + "🇮🇱")
        print("🎯 ISRAEL DEFENSE FORCES - MISSILE IDENTIFICATION SYSTEM")
        print("🇮🇱" + "="*70 + "🇮🇱")
        print(f"📷 Image: {os.path.basename(image_path)}")
        print(f"🚀 Detected Weapon: {weapon_info['weapon']}")
        print(f"🏴 Country/Origin: {weapon_info['country']}")
        print(f"🎯 Weapon Type: {weapon_info['weapon_type']}")
        print(f"📏 Range: {weapon_info['range']} ({israel_threat['range_km']}km)")
        print(f"🎯 Precision: {weapon_info['precision']}")
        print(f"⚠️  Threat Level: {weapon_info['threat_level']}")
        print(f"📊 AI Confidence: {confidence:.1%}")

        print(f"\n🇮🇱 ISRAELI TERRITORY THREAT ASSESSMENT:")
        print(f"🎯 Threat Coverage: {israel_threat['threat_level']}")
        print(f"🏙️  Cities at Risk: {israel_threat['coverage']}")

        if israel_threat['threatened_cities']:
            print(f"🏘️  Threatened Cities:")
            for i, city in enumerate(israel_threat['threatened_cities'][:10]):  # Show first 10
                print(f"   • {city}")
            if len(israel_threat['threatened_cities']) > 10:
                print(f"   • ... and {len(israel_threat['threatened_cities']) - 10} more cities")

        if israel_threat['strategic_sites_threatened']:
            print(f"🏭 Strategic Sites at Risk:")
            for site in israel_threat['strategic_sites_threatened']:
                print(f"   • {site}")

        # Special alerts for high-threat weapons
        if weapon_info['threat_level'] in ['CRITICAL', 'EXTREME']:
            print(f"\n🚨 HIGH PRIORITY ALERT 🚨")
            print(f"⚡ {weapon_info['weapon']} detected - immediate IDF assessment required")

        if israel_threat['range_km'] >= 350:
            print(f"🚨 NATIONAL THREAT: Can reach entire State of Israel including Eilat")
        elif israel_threat['range_km'] >= 200:
            print(f"🚨 MAJOR THREAT: Can reach most Israeli cities")
        elif israel_threat['range_km'] >= 70:
            print(f"⚠️  CENTRAL ISRAEL THREAT: Can reach Tel Aviv, Jerusalem, Haifa")
        elif israel_threat['range_km'] >= 40:
            print(f"⚠️  SOUTHERN THREAT: Can reach Ashdod, Ashkelon")

        if 'hypersonic' in weapon_info['weapon_type'].lower():
            print(f"⚡ HYPERSONIC THREAT: Iron Dome may be insufficient")

        if 'kamikaze' in weapon_info['weapon_type'].lower() or 'drone' in weapon_info['weapon_type'].lower():
            print(f"🛸 DRONE THREAT: Low-altitude, potential swarm attack")

        if 'MIRV' in weapon_info['weapon_type']:
            print(f"💥 MIRV THREAT: Multiple warheads, extremely dangerous")

        if class_probs:
            print(f"\n📈 Alternative Identifications:")
            sorted_probs = sorted(class_probs.items(), key=lambda x: x[1], reverse=True)
            for i, (weapon, prob) in enumerate(sorted_probs[:5]):
                if i == 0:
                    continue  # Skip the top prediction (already shown)
                threat_info = get_weapon_info(weapon)
                print(f"   {weapon} ({threat_info['weapon_type']}): {prob:.1%}")

        print("🇮🇱" + "="*70 + "🇮🇱")
        print("📞 REPORT TO IDF: *3456 or local emergency services")
        print("🚨 SHARE WITH SECURITY FORCES if confirmed threat")
        print("🇮🇱" + "="*70 + "🇮🇱\n")

    except Exception as e:
        print(f"❌ Classification failed: {e}")
        print("💡 Troubleshooting:")
        print("  1. Select AI model: python main.py --select-model")
        print("  2. Train classifier: python main.py --train")
        print("  3. Check image format (jpg, png, heic)")
        print("  4. Ensure clear image of missile/drone")


def run_gui():
    print("[INFO] Launching GUI viewer...")
    ui_viewer.launch_viewer()


def list_models():
    """Interactive model selection for Iranian missile detection"""
    model_list = list_all_models()
    if not model_list:
        return None

    print(f"\n🎯 Select your missile detection model:")
    print("💡 Tip: ONNX models typically work best for real-time detection")

    try:
        choice = int(input("\nSelect model by number: ")) - 1
        if 0 <= choice < len(model_list):
            selected = os.path.join('models/', model_list[choice])
            print(f"✅ Selected: {selected}")

            # Update config.yaml
            config = config_loader.load_config(CONFIG_PATH)
            config['paths']['onnx_model'] = selected
            with open(CONFIG_PATH, 'w') as f:
                import yaml
                yaml.dump(config, f, default_flow_style=False)

            print("🔄 Configuration updated successfully")
            print("🚀 Ready for missile detection!")
            return selected
        else:
            print("❌ Invalid selection")
            return None
    except (ValueError, IndexError):
        print("❌ Invalid input - please enter a number")
        return None

def select_input_image():
    """Interactive image selection for missile classification - optimized for mobile uploads"""
    import tkinter as tk
    from tkinter import filedialog, messagebox

    print("=== 🚀 MISSILE DETECTION - Select Image ===")
    print("📱 Upload photos from your phone or select from computer")

    # Try GUI file dialog first
    try:
        root = tk.Tk()
        root.withdraw()  # Hide the main window
        root.title("Tel Aviv Defense - Missile Identification")

        # Show instructions for mobile users
        messagebox.showinfo(
            "Mobile Upload Instructions",
            "📱 To upload from phone:\n"
            "1. Connect phone via USB/WiFi\n"
            "2. Copy photos to computer\n"
            "3. Select the missile image\n\n"
            "🎯 Supported: Iranian missiles, drones, rockets\n"
            "📸 Best results: Clear, well-lit images"
        )

        file_path = filedialog.askopenfilename(
            title="🚀 Select Missile/Drone Image for IFF Analysis",
            initialdir=os.path.expanduser("~/Pictures"),  # Start in Pictures folder
            filetypes=[
                ("Phone Images", "*.jpg *.jpeg *.heic *.png"),
                ("All Images", "*.jpg *.jpeg *.png *.bmp *.tiff *.gif *.heic *.webp"),
                ("JPEG files", "*.jpg *.jpeg"),
                ("PNG files", "*.png"),
                ("HEIC (iPhone)", "*.heic"),
                ("All files", "*.*")
            ]
        )

        if file_path:
            print(f"✓ Selected: {file_path}")
            # Check if it's a phone image
            if any(x in file_path.lower() for x in ['dcim', 'camera', 'whatsapp', 'telegram']):
                print("📱 Phone image detected - processing for missile identification...")
            return file_path
        else:
            print("[INFO] No file selected")
            return None

    except ImportError:
        # Fallback to manual input if tkinter not available
        print("[INFO] GUI not available, please enter path manually")
        print("📱 For mobile images, copy to computer first")
        file_path = input("Enter image path: ").strip().strip('"')  # Remove quotes
        if os.path.exists(file_path):
            return file_path
        else:
            print(f"[ERROR] File not found: {file_path}")
            return None

def list_all_models():
    """Enhanced model listing with support for multiple AI frameworks"""
    models_dir = 'models/'

    # Support multiple model formats
    model_extensions = ['.onnx', '.pt', '.pth', '.pkl', '.h5', '.tflite', '.pb']
    models = []

    for ext in model_extensions:
        models.extend([f for f in os.listdir(models_dir) if f.endswith(ext)])

    if not models:
        print("[!] No AI models found in models/")
        print("[INFO] Supported formats: ONNX, PyTorch, TensorFlow, scikit-learn")
        print("[INFO] Place your missile detection models in the models/ directory")
        return None

    print("=== 🎯 MISSILE DETECTION MODELS ===")
    print("Available AI models for Iranian missile identification:")

    # Group by type
    onnx_models = [m for m in models if m.endswith('.onnx')]
    pytorch_models = [m for m in models if m.endswith(('.pt', '.pth'))]
    other_models = [m for m in models if not m.endswith(('.onnx', '.pt', '.pth'))]

    model_list = []
    idx = 1

    if onnx_models:
        print(f"\n🔥 ONNX Models (Recommended):")
        for model in onnx_models:
            print(f"  {idx}. {model}")
            model_list.append(model)
            idx += 1

    if pytorch_models:
        print(f"\n🧠 PyTorch Models:")
        for model in pytorch_models:
            print(f"  {idx}. {model}")
            model_list.append(model)
            idx += 1

    if other_models:
        print(f"\n📊 Other Models:")
        for model in other_models:
            print(f"  {idx}. {model}")
            model_list.append(model)
            idx += 1

    return model_list


def ensure_directories():
    """Create necessary directories if they don't exist"""
    config = config_loader.load_config(CONFIG_PATH)
    directories = [
        config['paths']['render_output'],
        config['paths']['input_images'],
        config['paths']['train_images'],
        config['paths']['assets_folder'],
        os.path.dirname(config['paths']['classifier_pkl']),
        os.path.dirname(config['paths']['onnx_model'])
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"[INFO] Ensured directory exists: {directory}")


def main():
    parser = argparse.ArgumentParser(
        description="🇮🇱 9M83ME - Israel Defense Forces Threat Detection System",
        epilog="Protecting the entire State of Israel from Iranian missiles, drones, and REDFOR threats"
    )
    parser.add_argument('--render', action='store_true',
                       help='Render images from 3D models (optional)')
    parser.add_argument('--train', action='store_true',
                       help='Train classifier on REDFOR weapon images')
    parser.add_argument('--infer', type=str, metavar='IMAGE_PATH',
                       help='Classify a specific weapon/aircraft image')
    parser.add_argument('--infer-interactive', action='store_true',
                       help='Select and classify image interactively')
    parser.add_argument('--gui', action='store_true',
                       help='Launch the PyQt GUI viewer')
    parser.add_argument('--select-model', action='store_true',
                       help='Choose ONNX model for weapon detection')
    parser.add_argument('--batch-classify', action='store_true',
                       help='Classify all images in input_images folder')
    parser.add_argument('--mobile-batch', type=str, metavar='FOLDER_PATH',
                       help='Classify all images from mobile phone folder')

    args = parser.parse_args()

    print("🇮🇱 === 9M83ME - ISRAEL DEFENSE FORCES THREAT DETECTION === 🇮🇱")
    print("🎯 Protecting the entire State of Israel from Iranian/REDFOR threats")
    print("🏙️  Coverage: Jerusalem • Tel Aviv • Haifa • Beersheba • All Israeli cities")
    print()

    # Ensure directories exist
    ensure_directories()

    if args.select_model:
        list_models()
    elif args.render:
        run_renderer()
    elif args.train:
        run_training()
    elif args.infer:
        run_inference(args.infer)
    elif args.infer_interactive:
        image_path = select_input_image()
        if image_path:
            run_inference(image_path)
    elif args.batch_classify:
        print("🔍 Running batch classification on input_images folder...")
        from classifier import classify_folder
        classify_folder.classify_all_images(classify_folder.IMAGE_FOLDER)
    elif args.mobile_batch:
        print(f"📱 Processing mobile images from: {args.mobile_batch}")
        if os.path.exists(args.mobile_batch):
            from pathlib import Path
            mobile_folder = Path(args.mobile_batch)
            supported_formats = ['.jpg', '.jpeg', '.png', '.heic', '.webp']

            images = []
            for ext in supported_formats:
                images.extend(mobile_folder.glob(f"*{ext}"))
                images.extend(mobile_folder.glob(f"*{ext.upper()}"))

            if images:
                print(f"📸 Found {len(images)} images to analyze")
                for img_path in images:
                    print(f"\n📱 Processing: {img_path.name}")
                    run_inference(str(img_path))
            else:
                print("❌ No images found in mobile folder")
        else:
            print(f"❌ Mobile folder not found: {args.mobile_batch}")
    elif args.gui:
        run_gui()
    else:
        print("Use --help to see available commands")
        print("\n🇮🇱 Quick Start for Israel Defense:")
        print("  --select-model        Choose your missile detection AI model")
        print("  --infer-interactive   📱 Upload photo from phone for analysis")
        print("  --mobile-batch FOLDER 📱 Process entire phone camera folder")
        print("  --batch-classify      Analyze all images in input folder")
        print("  --gui                 Launch visual missile identification interface")
        print("\n📱 Mobile Usage for Israeli Citizens:")
        print("  1. Connect phone to computer (USB/WiFi)")
        print("  2. Copy suspicious missile/drone photos")
        print("  3. Run: python main.py --infer-interactive")
        print("  4. Get instant threat assessment for your area")
        print("  5. Report confirmed threats to IDF: *3456")
        print("\n🏙️  Protects: Jerusalem, Tel Aviv, Haifa, Beersheba, Ramat Gan, and ALL Israeli cities")


if __name__ == '__main__':
    main()

