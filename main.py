import argparse
import os
from classifier.train_classifier import main as train_main
from renderer import generate_views
from ui import ui_viewer
from config import config_loader

CONFIG_PATH = 'config/config.yaml'


def run_renderer():
    print("[INFO] Starting 3D model rendering...")
    generate_views.main()


def run_training():
    print("[INFO] Starting training process...")
    train_main()


def run_inference(image_path):
    """Run REDFOR weapon detection on a single image"""
    print(f"[INFO] Analyzing image for REDFOR weapons: {image_path}")
    if not os.path.exists(image_path):
        print(f"[ERROR] Image not found: {image_path}")
        return

    try:
        from classifier.classifier import classify_image, get_weapon_info

        # Get classification with confidence
        result, confidence, class_probs = classify_image(image_path, return_confidence=True)
        weapon_info = get_weapon_info(result)

        print("\n" + "="*50)
        print("🎯 REDFOR DETECTION RESULTS")
        print("="*50)
        print(f"📷 Image: {os.path.basename(image_path)}")
        print(f"🎯 Detected Weapon: {weapon_info['weapon']}")
        print(f"🌍 Country of Origin: {weapon_info['country']}")
        print(f"⚠️  Threat Level: {weapon_info['threat_level']}")
        print(f"📊 Confidence: {confidence:.2%}")

        if class_probs:
            print(f"\n📈 Top Predictions:")
            sorted_probs = sorted(class_probs.items(), key=lambda x: x[1], reverse=True)
            for weapon, prob in sorted_probs[:3]:
                print(f"   {weapon}: {prob:.2%}")

        print("="*50)

    except Exception as e:
        print(f"[ERROR] Classification failed: {e}")
        print("[INFO] Make sure you have:")
        print("  1. Selected an ONNX model (--select-model)")
        print("  2. Trained a classifier (--train)")
        print("  3. Valid image format (jpg, png, etc.)")


def run_gui():
    print("[INFO] Launching GUI viewer...")
    ui_viewer.launch_viewer()


def list_models():
    """Interactive ONNX model selection for REDFOR weapon detection"""
    models_dir = 'models/'
    models = [f for f in os.listdir(models_dir) if f.endswith('.onnx')]
    if not models:
        print("[!] No ONNX models found in models/")
        print("[INFO] Place your ONNX models in the models/ directory")
        return None

    print("=== REDFOR Detection System - Model Selection ===")
    print("Available ONNX Models for weapon identification:")
    for i, model in enumerate(models):
        print(f"  {i+1}. {model}")

    try:
        choice = int(input("Select model by number: ")) - 1
        if 0 <= choice < len(models):
            selected = os.path.join(models_dir, models[choice])
            print(f"✓ Selected: {selected}")

            # Update config.yaml
            config = config_loader.load_config(CONFIG_PATH)
            config['paths']['onnx_model'] = selected
            with open(CONFIG_PATH, 'w') as f:
                import yaml
                yaml.dump(config, f, default_flow_style=False)

            print("[INFO] Configuration updated successfully")
            return selected
        else:
            print("[ERROR] Invalid selection")
            return None
    except (ValueError, IndexError):
        print("[ERROR] Invalid input")
        return None

def select_input_image():
    """Interactive image selection for classification"""
    import tkinter as tk
    from tkinter import filedialog

    print("=== Select Input Image for REDFOR Detection ===")

    # Try GUI file dialog first
    try:
        root = tk.Tk()
        root.withdraw()  # Hide the main window

        file_path = filedialog.askopenfilename(
            title="Select weapon/aircraft image for classification",
            filetypes=[
                ("Image files", "*.jpg *.jpeg *.png *.bmp *.tiff *.gif"),
                ("JPEG files", "*.jpg *.jpeg"),
                ("PNG files", "*.png"),
                ("All files", "*.*")
            ]
        )

        if file_path:
            print(f"✓ Selected: {file_path}")
            return file_path
        else:
            print("[INFO] No file selected")
            return None

    except ImportError:
        # Fallback to manual input if tkinter not available
        print("[INFO] GUI not available, please enter path manually")
        file_path = input("Enter image path: ").strip()
        if os.path.exists(file_path):
            return file_path
        else:
            print(f"[ERROR] File not found: {file_path}")
            return None


def ensure_directories():
    """Create necessary directories if they don't exist"""
    config = config_loader.load_config(CONFIG_PATH)
    directories = [
        config['paths']['render_output'],
        config['paths']['input_images'],
        config['paths']['train_images'],
        config['paths']['assets_folder'],
        os.path.dirname(config['paths']['classifier_pkl']),
        os.path.dirname(config['paths']['onnx_model'])
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"[INFO] Ensured directory exists: {directory}")


def main():
    parser = argparse.ArgumentParser(
        description="9M83ME - Anti-REDFOR Detection System",
        epilog="Identifies Russian/Iranian/Chinese weapons and aircraft for IFF purposes"
    )
    parser.add_argument('--render', action='store_true',
                       help='Render images from 3D models (optional)')
    parser.add_argument('--train', action='store_true',
                       help='Train classifier on REDFOR weapon images')
    parser.add_argument('--infer', type=str, metavar='IMAGE_PATH',
                       help='Classify a specific weapon/aircraft image')
    parser.add_argument('--infer-interactive', action='store_true',
                       help='Select and classify image interactively')
    parser.add_argument('--gui', action='store_true',
                       help='Launch the PyQt GUI viewer')
    parser.add_argument('--select-model', action='store_true',
                       help='Choose ONNX model for weapon detection')
    parser.add_argument('--batch-classify', action='store_true',
                       help='Classify all images in input_images folder')

    args = parser.parse_args()

    print("=== 9M83ME Anti-REDFOR Detection System ===")
    print("Identifying Russian/Iranian/Chinese weapons for IFF")
    print()

    # Ensure directories exist
    ensure_directories()

    if args.select_model:
        list_models()
    elif args.render:
        run_renderer()
    elif args.train:
        run_training()
    elif args.infer:
        run_inference(args.infer)
    elif args.infer_interactive:
        image_path = select_input_image()
        if image_path:
            run_inference(image_path)
    elif args.batch_classify:
        print("[INFO] Running batch classification on input_images folder...")
        from classifier import classify_folder
        classify_folder.classify_all_images(classify_folder.IMAGE_FOLDER)
    elif args.gui:
        run_gui()
    else:
        print("Use --help to see available commands")
        print("\nQuick start:")
        print("  --select-model     Choose your ONNX detection model")
        print("  --infer-interactive Select and classify an image")
        print("  --batch-classify   Classify all images in input folder")
        print("  --gui              Launch visual interface")


if __name__ == '__main__':
    main()

