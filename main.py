import argparse
import os
import sys
from pathlib import Path
import yaml
from classifier.train_classifier import main as train_main
from classifier.classifier import classify_image
from renderer import generate_views
from ui import ui_viewer
from config import config_loader

CONFIG_PATH = 'config/config.yaml'


def run_renderer():
    print("[INFO] Starting 3D model rendering...")
    generate_views.main()


def run_training():
    print("[INFO] Starting training process...")
    train_main()


def run_inference(image_path):
    print(f"[INFO] Classifying image: {image_path}")
    if not os.path.exists(image_path):
        print(f"[ERROR] Image not found: {image_path}")
        return
    try:
        result = classify_image(image_path)
        print(f" Prediction: {result}")
    except Exception as e:
        print(f"[ERROR] Classification failed: {e}")


def run_gui():
    print("[INFO] Launching GUI viewer...")
    ui_viewer.launch_viewer()


def list_models():
    models_dir = 'models/'
    models = [f for f in os.listdir(models_dir) if f.endswith('.onnx')]
    if not models:
        print("[!] No ONNX models found in models/")
        return None
    print("Available ONNX Models:")
    for i, model in enumerate(models):
        print(f"  {i+1}. {model}")
    choice = int(input("Select model by number: ")) - 1
    selected = os.path.join(models_dir, models[choice])
    print(f" Selected: {selected}")

    # Update config.yaml
    config = config_loader.load_config(CONFIG_PATH)
    config['paths']['onnx_model'] = selected
    with open(CONFIG_PATH, 'w') as f:
        import yaml
        yaml.dump(config, f)

    return selected


def ensure_directories():
    """Create necessary directories if they don't exist"""
    config = config_loader.load_config(CONFIG_PATH)
    directories = [
        config['paths']['render_output'],
        config['paths']['input_images'],
        config['paths']['train_images'],
        config['paths']['assets_folder'],
        os.path.dirname(config['paths']['classifier_pkl']),
        os.path.dirname(config['paths']['onnx_model'])
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"[INFO] Ensured directory exists: {directory}")


def main():
    parser = argparse.ArgumentParser(description="9M83ME - Drone/Aircraft Classifier")
    parser.add_argument('--render', action='store_true', help='Render images from 3D models')
    parser.add_argument('--train', action='store_true', help='Train classifier on generated images')
    parser.add_argument('--infer', type=str, help='Run classifier on a specific image')
    parser.add_argument('--gui', action='store_true', help='Launch the PyQt GUI viewer')
    parser.add_argument('--select-model', action='store_true', help='Choose ONNX model from available list')

    args = parser.parse_args()
    selected_model_path = None

    # Ensure directories exist
    ensure_directories()

    if args.select_model:
        selected_model_path = list_models()
    if args.render:
        run_renderer()
    elif args.train:
        run_training()
    elif args.infer:
        run_inference(args.infer)
    elif args.gui:
        run_gui()
    else:
        parser.print_help()


if __name__ == '__main__':
    main()

