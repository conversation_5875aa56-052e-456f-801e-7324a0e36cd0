<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="4.0">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectTypeGuids>{a41c8ea1-112a-4a2d-9f91-29557995525f};{888888a0-9f3d-457c-b088-3a5042f75d52}</ProjectTypeGuids>
    <ProjectHome>.</ProjectHome>
    <StartupFile>gui_main.py</StartupFile>
    <SearchPath>
    </SearchPath>
    <WorkingDirectory>.</WorkingDirectory>
    <OutputPath>.</OutputPath>
    <Name>9M83ME - Israel Defense Forces Threat Detection</Name>
    <RootNamespace>IsraelDefense</RootNamespace>
    <InterpreterId>Global|PythonCore|3.10</InterpreterId>
    <LaunchProvider>Standard Python launcher</LaunchProvider>
    <CommandLineArguments></CommandLineArguments>
    <EnableNativeCodeDebugging>False</EnableNativeCodeDebugging>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
    <DebugSymbols>true</DebugSymbols>
    <EnableUnmanagedDebugging>false</EnableUnmanagedDebugging>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
    <DebugSymbols>true</DebugSymbols>
    <EnableUnmanagedDebugging>false</EnableUnmanagedDebugging>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="gui_main.py" />
    <Compile Include="build_exe.py" />
    <Compile Include="main.py" />
    <Compile Include="test_pc_compatibility.py" />
    <Compile Include="tel_aviv_defense_test.py" />
    <Compile Include="classifier\__init__.py" />
    <Compile Include="classifier\classifier.py" />
    <Compile Include="classifier\classify_folder.py" />
    <Compile Include="classifier\feature_extractor.py" />
    <Compile Include="classifier\train_classifier.py" />
    <Compile Include="config\__init__.py" />
    <Compile Include="config\config_loader.py" />
    <Compile Include="ui\__init__.py" />
    <Compile Include="ui\gl_widget.py" />
    <Compile Include="ui\ui_viewer.py" />
    <Compile Include="renderer\__init__.py" />
    <Compile Include="renderer\generate_views.py" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="README.md" />
    <Content Include="PC_INSTALLATION_GUIDE.md" />
    <Content Include="VISUAL_STUDIO_2022_GUIDE.md" />
    <Content Include="ISRAEL_DEFENSE_EXAMPLE.md" />
    <Content Include="environment.yml" />
    <Content Include="requirements.txt" />
    <Content Include="config\config.yaml" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="models\" />
    <Folder Include="data\" />
    <Folder Include="data\input_images\" />
    <Folder Include="data\train_images\" />
    <Folder Include="data\generated_views\" />
    <Folder Include="assets\" />
    <Folder Include="classifier\" />
    <Folder Include="config\" />
    <Folder Include="ui\" />
    <Folder Include="renderer\" />
  </ItemGroup>
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\Python Tools\Microsoft.PythonTools.targets" />
  <!-- Uncomment the CoreCompile target to enable the Build command in
       Visual Studio and specify your pre- and post-build commands in
       the BeforeBuild and AfterBuild targets below. -->
  <!--<Target Name="CoreCompile" />-->
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
</Project>
