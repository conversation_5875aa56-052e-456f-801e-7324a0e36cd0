import os
import numpy as np
import joblib
from PIL import Image
import onnxruntime as ort
from sklearn.linear_model import LogisticRegression
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report
from pathlib import Path
from config import config_loader
    
# Load configuration
config = config_loader.load_config()
DATA_DIR = Path(config['paths']['train_images'])
MODELS_DIR = Path(config['paths']['models'])
MODEL_PATH = MODELS_DIR / "vit_classifier.pkl"
LABELS_PATH = MODELS_DIR / "class_labels.npy"
SUPPORTED_FORMATS = (".jpg", ".jpeg", ".png", ".bmp", ".tiff", ".gif")

def find_onnx_model():
    """Automatically find ONNX models in the models directory."""
    if not MODELS_DIR.exists():
        raise FileNotFoundError(f"Models directory not found: {MODELS_DIR}")
    
    onnx_models = list(MODELS_DIR.glob("*.onnx"))
    if not onnx_models:
        raise FileNotFoundError("No ONNX models found in models directory")
    
    # If multiple models exist, use the most recently modified one
    return str(sorted(onnx_models, key=lambda x: x.stat().st_mtime, reverse=True)[0])

def preprocess_image(image_path, size=(224, 224)):
    img = Image.open(image_path).convert("RGB")
    img = img.resize(size)
    arr = np.array(img).astype(np.float32) / 255.0
    arr = arr.transpose(2, 0, 1)  # Channels first
    arr = np.expand_dims(arr, 0)  # Add batch dimension
    return arr

def extract_features_onnx(image_path, session, input_name, output_name):
    arr = preprocess_image(image_path)
    outputs = session.run([output_name], {input_name: arr})
    return outputs[0].flatten()

def train_classifier(data_dir=None):
    """Train the classifier and return the trained model and class labels."""
    if data_dir is None:
        data_dir = DATA_DIR

    print("[INFO] Finding ONNX model...")
    onnx_path = find_onnx_model()
    print(f"[INFO] Using ONNX model: {onnx_path}")

    print("[INFO] Loading ONNX model...")
    session = ort.InferenceSession(onnx_path, providers=['CPUExecutionProvider'])
    input_name = session.get_inputs()[0].name
    output_name = session.get_outputs()[0].name

    X, y, class_names = [], [], []

