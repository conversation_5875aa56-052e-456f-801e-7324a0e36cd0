from pathlib import Path
import joblib
from config import config_loader
from feature_extractor import extract_features  # Relative import

# Load config
config = config_loader.load_config()
IMAGE_FOLDER = Path(config['paths']['input_images'])
CLASSIFIER_PATH = Path(config['paths']['classifier_pkl'])

# Load trained classifier with error handling
def load_classifier():
    if not CLASSIFIER_PATH.exists():
        raise FileNotFoundError(f"Classifier not found at: {CLASSIFIER_PATH}")
    try:
        return joblib.load(CLASSIFIER_PATH)
    except Exception as e:
        raise RuntimeError(f"Failed to load classifier: {e}")

classifier = load_classifier()

def classify_all_images(image_folder):
    supported_formats = ('.png', '.jpg', '.jpeg')
    results = []

    # Check if folder exists
    if not image_folder.exists():
        print(f"[ERROR] Image folder not found: {image_folder}")
        return results

    # Check if folder is empty
    files = list(image_folder.glob("*"))
    if not files:
        print(f"[WARNING] No files found in: {image_folder}")
        return results

    for image_path in image_folder.glob("*"):
        if image_path.suffix.lower() in supported_formats:
            try:
                features = extract_features(str(image_path))
                prediction = classifier.predict([features])[0]
                print(f"[✓] {image_path.name} → {prediction}")
                results.append((image_path.name, prediction))
            except Exception as e:
                print(f"[ERROR] Failed to classify {image_path.name}: {e}")

    return results

if __name__ == '__main__':
    print(f"[INFO] Classifying images in: {IMAGE_FOLDER}")
    classify_all_images(IMAGE_FOLDER)
