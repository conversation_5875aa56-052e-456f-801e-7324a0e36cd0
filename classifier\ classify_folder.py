import os
from pathlib import Path
import sys
sys.path.append(os.path.dirname(os.path.dirname(__file__)))
import joblib
from config import config_loader
from classifier.feature_extractor import extract_features


# Load config
config = config_loader.load_config()
IMAGE_FOLDER = Path(config['paths']['input_images'])
CLASSIFIER_PATH = Path(config['paths']['classifier_pkl'])

# Load trained classifier
classifier = joblib.load(CLASSIFIER_PATH)

def classify_all_images(image_folder):
    supported_formats = ('.png', '.jpg', '.jpeg')
    results = []

    for image_path in image_folder.glob("*"):
        if image_path.suffix.lower() in supported_formats:
            features = extract_features(str(image_path))
            prediction = classifier.predict([features])[0]
            print(f"[✓] {image_path.name} → {prediction}")
            results.append((image_path.name, prediction))

    return results

if __name__ == '__main__':
    print(f"[INFO] Classifying images in: {IMAGE_FOLDER}")
    classify_all_images(IMAGE_FOLDER)
