from PyQt5.QtWidgets import QMainWindow, QWidget, QVBoxLayout, QPushButton, QLabel, QSlider
from PyQt5.QtCore import Qt
from ui.gl_widget import DroneGLWidget

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Weapon classifier Viewer")
        self.setGeometry(200, 100, 800, 600)

        self.gl_widget = DroneGLWidget()

        self.label = QLabel("Distance: 100m")
        self.slider = QSlider(Qt.Horizontal)
        self.slider.setMinimum(10)
        self.slider.setMaximum(500)
        self.slider.setValue(100)
        self.slider.valueChanged.connect(self.on_distance_changed)

        self.classify_button = QPushButton("Classify Image")
        self.classify_button.clicked.connect(self.gl_widget.run_classification)

        layout = QVBoxLayout()
        layout.addWidget(self.gl_widget)
        layout.addWidget(self.label)
        layout.addWidget(self.slider)
        layout.addWidget(self.classify_button)

        container = QWidget()
        container.setLayout(layout)
        self.setCentralWidget(container)

    def on_distance_changed(self, value):
        self.label.setText(f"Distance: {value}m")
        self.gl_widget.set_distance(value)

def launch_viewer():
    """Launch the PyQt GUI viewer"""
    from PyQt5.QtWidgets import QApplication
    import sys
    
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    return app.exec_()
