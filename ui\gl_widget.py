from PyQt5.QtWidgets import QOpenGLWidget
from PyQt5.QtGui import QImage, QPainter
from PyQt5.QtCore import Qt
import joblib
from classifier.classifier import classify_image, CLASSIFIER_PATH

class DroneGLWidget(QOpenGLWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.image = None
        self.image_path = None
        self.distance = 100  # Default in meters
        self.classification_result = "Unknown"
        self.classifier_model = joblib.load(CLASSIFIER_PATH)

    def load_image(self, file_path):
        # Optionally, filter by supported extensions
        supported_formats = ['.png', '.jpg', '.jpeg', '.bmp', '.gif', '.tiff']
        if not any(file_path.lower().endswith(ext) for ext in supported_formats):
            self.image = None
            self.image_path = None
            self.classification_result = "Unsupported file format"
            self.update()
            return

        img = QImage(file_path)
        if img.isNull():
            self.image = None
            self.image_path = None
            self.classification_result = "Failed to load image"
        else:
            self.image = img
            self.image_path = file_path
            self.classification_result = "Unknown"
        self.update()

    def set_distance(self, distance):
        self.distance = distance
        self.update()

    def run_classification(self):
        if self.image_path and self.classifier_model:
            try:
                result = classify_image(self.image_path, self.classifier_model)
                self.classification_result = str(result)
            except Exception as e:
                self.classification_result = f"Error: {e}"
        else:
            self.classification_result = "No image loaded"
        self.update()

    def paintEvent(self, event):
        painter = QPainter(self)
        painter.fillRect(self.rect(), Qt.black)

        if not self.image or self.image.isNull():
            painter.setPen(Qt.white)
            painter.drawText(self.rect(), Qt.AlignCenter, "No image loaded")
        else:
            scaled = self.image.scaled(self.width(), self.height(), Qt.KeepAspectRatio, Qt.SmoothTransformation)
            if self.distance > 300:
                scaled = scaled.convertToFormat(QImage.Format_Grayscale8)
            painter.drawImage(self.rect(), scaled)
            painter.setPen(Qt.red)
            painter.drawText(10, 20, f"Prediction: {self.classification_result}")
